#!/usr/bin/env node

/**
 * 显示Chrome MCP Server可用工具的脚本
 */

import fs from 'fs';
import path from 'path';

function showAvailableTools() {
  console.log('🛠️  Chrome MCP Server 可用工具列表\n');
  console.log('=' .repeat(60));

  const tools = [
    {
      category: '📊 浏览器管理',
      tools: [
        'get_windows_and_tabs - 列出所有浏览器窗口和标签页',
        'chrome_navigate - 导航到URL并控制视口',
        'chrome_close_tabs - 关闭特定标签页或窗口',
        'chrome_go_back_or_forward - 浏览器导航控制',
        'chrome_inject_script - 向网页注入内容脚本',
        'chrome_send_command_to_inject_script - 向注入的内容脚本发送命令'
      ]
    },
    {
      category: '📸 截图和视觉',
      tools: [
        'chrome_screenshot - 高级截图捕获，支持元素定位、全页面和自定义尺寸'
      ]
    },
    {
      category: '🌐 网络监控',
      tools: [
        'chrome_network_capture_start/stop - webRequest API网络捕获',
        'chrome_network_debugger_start/stop - 调试器API，包含响应体',
        'chrome_network_request - 发送自定义HTTP请求'
      ]
    },
    {
      category: '🔍 内容分析',
      tools: [
        'search_tabs_content - AI驱动的浏览器标签页语义搜索',
        'chrome_get_web_content - 从页面提取HTML/文本内容',
        'chrome_get_interactive_elements - 查找可点击元素',
        'chrome_console - 捕获和检索浏览器标签页的控制台输出'
      ]
    },
    {
      category: '🎯 交互操作',
      tools: [
        'chrome_click_element - 使用CSS选择器点击元素',
        'chrome_fill_or_select - 填写表单和选择选项',
        'chrome_keyboard - 模拟键盘输入和快捷键'
      ]
    },
    {
      category: '📚 数据管理',
      tools: [
        'chrome_history - 使用时间过滤器搜索浏览器历史',
        'chrome_bookmark_search - 通过关键词查找书签',
        'chrome_bookmark_add - 添加新书签，支持文件夹',
        'chrome_bookmark_delete - 删除书签'
      ]
    }
  ];

  tools.forEach((category, index) => {
    console.log(`\n${category.category} (${category.tools.length} 个工具)`);
    console.log('-'.repeat(40));
    category.tools.forEach((tool, toolIndex) => {
      console.log(`  ${toolIndex + 1}. ${tool}`);
    });
  });

  const totalTools = tools.reduce((sum, category) => sum + category.tools.length, 0);
  
  console.log('\n' + '='.repeat(60));
  console.log(`🎯 总计: ${totalTools} 个工具可用`);
  console.log('\n💡 特色功能:');
  console.log('  • SIMD加速的AI向量运算');
  console.log('  • 内置向量数据库进行语义搜索');
  console.log('  • 跨标签页上下文管理');
  console.log('  • 支持HTTP和STDIO两种MCP连接方式');
  console.log('  • 与现有浏览器环境无缝集成');
  
  console.log('\n🚀 使用方法:');
  console.log('  1. 确保Chrome扩展已安装并连接');
  console.log('  2. 配置MCP客户端连接到 http://127.0.0.1:12306/mcp');
  console.log('  3. 通过AI助手调用这些工具实现浏览器自动化');
}

// 检查服务器状态
function checkServerStatus() {
  console.log('🔍 检查服务器状态...\n');
  
  // 检查构建文件是否存在
  const serverPath = './app/native-server/dist/index.js';
  const extensionPath = './app/chrome-extension/.output/chrome-mv3/manifest.json';
  
  if (fs.existsSync(serverPath)) {
    console.log('✅ 原生服务器已构建');
  } else {
    console.log('❌ 原生服务器未构建');
  }
  
  if (fs.existsSync(extensionPath)) {
    console.log('✅ Chrome扩展已构建');
  } else {
    console.log('❌ Chrome扩展未构建');
  }
  
  console.log('');
}

// 主函数
function main() {
  console.log('🎉 Chrome MCP Server 项目演示\n');
  
  checkServerStatus();
  showAvailableTools();
  
  console.log('\n📖 更多信息请查看:');
  console.log('  • README.md - 项目介绍和快速开始');
  console.log('  • DEMO_GUIDE.md - 详细演示指南');
  console.log('  • docs/TOOLS.md - 完整工具API文档');
}

main();
