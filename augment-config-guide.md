# Augment中配置Chrome MCP Server指南 🚀

## 📋 前提条件检查

✅ **已完成的步骤**：
- mcp-chrome-bridge@1.0.29 已全局安装
- 原生消息主机已注册
- Chrome扩展已构建

## 🔧 Augment配置方法

### 方法1：Streamable HTTP连接（推荐）

**优势**：
- 配置简单
- 连接稳定
- 支持多客户端同时连接

**配置步骤**：

1. **启动MCP服务器**：
   ```bash
   mcp-chrome-bridge
   ```
   服务器将在 `http://127.0.0.1:12306` 启动

2. **在Augment中添加配置**：
   ```json
   {
     "mcpServers": {
       "chrome-mcp-server": {
         "type": "streamableHttp",
         "url": "http://127.0.0.1:12306/mcp",
         "name": "Chrome浏览器控制",
         "description": "通过Chrome扩展控制浏览器的MCP服务器"
       }
     }
   }
   ```

### 方法2：STDIO连接

**配置**：
```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "node",
      "args": [
        "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"
      ],
      "name": "Chrome浏览器控制(STDIO)",
      "description": "通过STDIO连接的Chrome MCP服务器"
    }
  }
}
```

## 🎯 Chrome扩展安装

**重要**：MCP服务器需要Chrome扩展配合工作

1. **打开Chrome扩展管理**：
   - 访问 `chrome://extensions/`
   - 启用"开发者模式"

2. **加载扩展**：
   - 点击"加载已解压的扩展程序"
   - 选择文件夹：`D:\Test\mcp-chrome\app\chrome-extension\.output\chrome-mv3`

3. **连接扩展**：
   - 点击扩展图标
   - 点击"Connect"按钮
   - 确认连接状态为"已连接"

## 🧪 测试配置

### 验证服务器运行
```bash
# 检查服务器是否运行
curl http://127.0.0.1:12306/mcp
# 应该返回：{"error":"Invalid or missing MCP session ID for SSE."}
```

### 在Augment中测试
配置完成后，您可以在Augment中使用以下命令测试：

```
请帮我截取当前网页的截图
```

```
请列出我当前打开的所有浏览器标签页
```

```
请帮我搜索我的浏览历史，找到关于"AI"的网页
```

## 🛠️ 可用工具预览

配置成功后，您将获得21个强大的浏览器控制工具：

### 📊 浏览器管理
- `get_windows_and_tabs` - 获取窗口和标签页列表
- `chrome_navigate` - 导航到指定URL
- `chrome_close_tabs` - 关闭标签页
- `chrome_go_back_or_forward` - 前进/后退
- `chrome_inject_script` - 注入JavaScript
- `chrome_send_command_to_inject_script` - 发送命令到脚本

### 📸 截图功能
- `chrome_screenshot` - 网页截图（支持全页面、元素截图）

### 🌐 网络监控
- `chrome_network_capture_start/stop` - 网络请求监控
- `chrome_network_debugger_start/stop` - 调试器网络监控
- `chrome_network_request` - 发送HTTP请求

### 🔍 内容分析
- `search_tabs_content` - AI语义搜索标签页内容
- `chrome_get_web_content` - 获取网页内容
- `chrome_get_interactive_elements` - 获取可交互元素
- `chrome_console` - 获取控制台日志

### 🎯 交互操作
- `chrome_click_element` - 点击页面元素
- `chrome_fill_or_select` - 填写表单
- `chrome_keyboard` - 键盘输入

### 📚 数据管理
- `chrome_history` - 搜索浏览历史
- `chrome_bookmark_search` - 搜索书签
- `chrome_bookmark_add` - 添加书签
- `chrome_bookmark_delete` - 删除书签

## 🚨 故障排除

### 常见问题

1. **连接失败**：
   - 确认服务器正在运行：`netstat -an | findstr 12306`
   - 检查Chrome扩展是否已安装并连接

2. **工具调用失败**：
   - 确认Chrome扩展状态为"已连接"
   - 检查目标网页是否已加载完成

3. **权限问题**：
   - 确认Chrome扩展有足够权限
   - 某些网站可能阻止脚本注入

### 日志查看
```bash
# 查看服务器日志
mcp-chrome-bridge --verbose
```

## 🎉 配置完成

完成以上配置后，您就可以在Augment中通过自然语言控制Chrome浏览器了！

**示例对话**：
- "帮我打开百度首页并搜索'人工智能'"
- "截取当前页面的截图"
- "帮我关闭所有包含'广告'的标签页"
- "分析我最近访问的技术相关网站"
