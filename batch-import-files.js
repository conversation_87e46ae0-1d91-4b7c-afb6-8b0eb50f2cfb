// 批量导入文件列表
const filesToImport = [
  "Workflow-X100_Vyuerhuibenpro_step2video_1-draft-6353.zip",
  "Workflow-X100_Vyuerhuibenpro_step3video_1-draft-6375.zip",
  "Workflow-X100_Vyuerhuiben_pro_video_1_1-draft-6331.zip",
  "Workflow-X102_Vxiangsubianqingxi_step1_1-draft-6396.zip",
  "Workflow-X102_Vxiangsubianqingxi_step3_1-draft-6685.zip",
  "Workflow-X105_V54xiangsufeng_video_1-draft-6707.zip",
  "Workflow-X106_xiangsufeng_jimeng_step1_1-draft-6729.zip",
  "Workflow-X106_xiangsufeng_jimeng_step2_1-draft-6751.zip",
  "Workflow-X106_xiangsufeng_jimeng_step3_1-draft-6773.zip",
  "Workflow-X109_<PERSON><PERSON><PERSON><PERSON><PERSON>_pro_video_1-draft-6796.zip",
  "Workflow-X110_Vdaojiaoxuanxue_video_1-draft-6818.zip",
  "Workflow-X111_wordstudy_new_video_1-draft-6841.zip",
  "Workflow-X112_Vxioarenguo_01_video_1_1-draft-6861.zip",
  "Workflow-X112_Vxioarenguo_02_video_1-draft-6883.zip",
  "Workflow-X112_Vxioarenguo_03_video_1-draft-6905.zip",
  "Workflow-X11_Tdouyin2_table_1-draft-5221.zip",
  "Workflow-X125_children_to_leep_video_1-draft-7261.zip",
  "Workflow-X125_children_to_leep_video_1_1-draft-7389.zip",
  "Workflow-X126_gangqing_video_1-draft-7282.zip",
  "Workflow-X12_Tdouyin4_table_1-draft-5258.zip"
  // ... 更多文件将在实际执行中处理
];

console.log(`准备导入 ${filesToImport.length} 个文件...`);
