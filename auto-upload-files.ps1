# 全自动批量上传文件到扣子平台
# 使用PowerShell自动化Windows文件对话框

param(
    [string]$FilePath = "F:\AIprogram\cozeworkflows\工作流200+合集分享",
    [int]$DelaySeconds = 2
)

# 加载Windows Forms和SendKeys
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 获取所有zip文件
$zipFiles = Get-ChildItem -Path $FilePath -Filter "*.zip" | Sort-Object Name
Write-Host "找到 $($zipFiles.Count) 个zip文件"

# 导入按钮点击函数
function Click-ImportButton {
    Write-Host "正在点击导入按钮..."
    # 这里需要配合Chrome MCP来点击导入按钮
    # 或者使用坐标点击
}

# 处理文件选择对话框
function Select-File {
    param([string]$FileName)
    
    Write-Host "等待文件选择对话框出现..."
    Start-Sleep -Seconds 2
    
    # 等待文件对话框出现
    $timeout = 10
    $elapsed = 0
    while ($elapsed -lt $timeout) {
        $fileDialog = Get-Process | Where-Object { $_.MainWindowTitle -like "*打开*" -or $_.MainWindowTitle -like "*Open*" }
        if ($fileDialog) {
            Write-Host "检测到文件对话框"
            break
        }
        Start-Sleep -Seconds 1
        $elapsed++
    }
    
    if ($elapsed -ge $timeout) {
        Write-Host "未检测到文件对话框，跳过此文件"
        return $false
    }
    
    # 发送路径
    [System.Windows.Forms.SendKeys]::SendWait("^l")  # Ctrl+L 打开地址栏
    Start-Sleep -Milliseconds 500
    [System.Windows.Forms.SendKeys]::SendWait($FilePath)
    [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
    Start-Sleep -Seconds 1
    
    # 输入文件名
    [System.Windows.Forms.SendKeys]::SendWait($FileName)
    Start-Sleep -Milliseconds 500
    [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
    
    Write-Host "已选择文件: $FileName"
    return $true
}

# 主循环
$successCount = 0
$failCount = 0

foreach ($file in $zipFiles) {
    Write-Host "`n正在处理文件 $($successCount + 1)/$($zipFiles.Count): $($file.Name)"
    
    try {
        # 1. 点击导入按钮 (需要配合Chrome MCP)
        Write-Host "请手动点击导入按钮，然后按任意键继续..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        # 2. 点击上传区域 (需要配合Chrome MCP)
        Write-Host "请手动点击上传区域，然后按任意键继续..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        # 3. 自动处理文件选择对话框
        if (Select-File -FileName $file.Name) {
            # 4. 等待上传完成，然后点击最终导入按钮
            Write-Host "等待文件上传完成..."
            Start-Sleep -Seconds 3
            
            Write-Host "请手动点击最终的导入按钮，然后按任意键继续..."
            $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            
            $successCount++
            Write-Host "文件 $($file.Name) 导入成功"
        } else {
            $failCount++
            Write-Host "文件 $($file.Name) 导入失败"
        }
        
        # 等待页面刷新
        Start-Sleep -Seconds $DelaySeconds
        
    } catch {
        Write-Host "处理文件 $($file.Name) 时出错: $($_.Exception.Message)"
        $failCount++
    }
}

Write-Host "`n批量导入完成!"
Write-Host "成功: $successCount 个文件"
Write-Host "失败: $failCount 个文件"
Write-Host "总计: $($zipFiles.Count) 个文件"
