#!/usr/bin/env node

/**
 * 新电脑上设置Chrome MCP Server的验证脚本
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

function findGlobalInstallPath() {
  console.log('🔍 查找mcp-chrome-bridge全局安装路径...\n');
  
  const possiblePaths = [];
  const username = os.userInfo().username;
  
  // npm路径
  const npmPath = `C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge`;
  possiblePaths.push({
    type: 'npm',
    path: npmPath,
    stdioPath: path.join(npmPath, 'dist', 'mcp', 'mcp-server-stdio.js')
  });
  
  // pnpm路径（可能的版本）
  for (let i = 5; i <= 10; i++) {
    const pnpmPath = `C:\\Users\\<USER>\\AppData\\Local\\pnpm\\global\\${i}\\node_modules\\mcp-chrome-bridge`;
    possiblePaths.push({
      type: `pnpm-v${i}`,
      path: pnpmPath,
      stdioPath: path.join(pnpmPath, 'dist', 'mcp', 'mcp-server-stdio.js')
    });
  }
  
  // 检查哪个路径存在
  const validPaths = possiblePaths.filter(p => fs.existsSync(p.stdioPath));
  
  console.log('📍 检查结果:');
  possiblePaths.forEach(p => {
    const exists = fs.existsSync(p.stdioPath);
    console.log(`  ${exists ? '✅' : '❌'} ${p.type}: ${p.path}`);
  });
  
  return validPaths;
}

function generateConfig(validPaths) {
  console.log('\n📋 生成MCP配置...\n');
  
  if (validPaths.length === 0) {
    console.log('❌ 未找到有效的安装路径！');
    console.log('💡 请先安装: npm install -g mcp-chrome-bridge');
    return;
  }
  
  validPaths.forEach((p, index) => {
    console.log(`🔧 配置${index + 1} (${p.type}):`);
    
    const config = {
      mcpServers: {
        "chrome-mcp-stdio": {
          command: "node",
          args: [p.stdioPath.replace(/\\/g, '\\\\')],
          name: "Chrome浏览器控制",
          description: "通过STDIO连接的Chrome MCP服务器"
        }
      }
    };
    
    console.log('```json');
    console.log(JSON.stringify(config, null, 2));
    console.log('```\n');
  });
}

function checkInstallation() {
  console.log('🔍 检查安装状态...\n');
  
  try {
    // 检查npm全局安装
    try {
      const npmResult = execSync('npm list -g mcp-chrome-bridge', { encoding: 'utf8' });
      console.log('✅ npm全局安装检测:');
      console.log(npmResult);
    } catch (error) {
      console.log('❌ npm全局安装未找到');
    }
    
    // 检查pnpm全局安装
    try {
      const pnpmResult = execSync('pnpm list -g mcp-chrome-bridge', { encoding: 'utf8' });
      console.log('✅ pnpm全局安装检测:');
      console.log(pnpmResult);
    } catch (error) {
      console.log('❌ pnpm全局安装未找到');
    }
    
  } catch (error) {
    console.log('⚠️  无法检查安装状态');
  }
}

function showSetupInstructions() {
  console.log('📝 完整设置步骤:\n');
  
  console.log('1️⃣ 安装mcp-chrome-bridge:');
  console.log('   npm install -g mcp-chrome-bridge');
  console.log('   # 或者');
  console.log('   pnpm install -g mcp-chrome-bridge\n');
  
  console.log('2️⃣ 下载Chrome扩展:');
  console.log('   • 访问: https://github.com/hangwin/mcp-chrome/releases');
  console.log('   • 下载最新版本的扩展文件');
  console.log('   • 解压到本地文件夹\n');
  
  console.log('3️⃣ 安装Chrome扩展:');
  console.log('   • 打开 chrome://extensions/');
  console.log('   • 启用"开发者模式"');
  console.log('   • 点击"加载已解压的扩展程序"');
  console.log('   • 选择解压后的扩展文件夹\n');
  
  console.log('4️⃣ 连接扩展:');
  console.log('   • 点击扩展图标');
  console.log('   • 点击"Connect"按钮');
  console.log('   • 确认显示"服务运行中"\n');
  
  console.log('5️⃣ 在Augment中添加上述生成的配置\n');
  
  console.log('6️⃣ 测试功能:');
  console.log('   "请列出当前打开的所有浏览器标签页"');
  console.log('   "请帮我截取当前网页的截图"\n');
}

function showTroubleshooting() {
  console.log('🚨 常见问题解决:\n');
  
  console.log('• 如果安装失败:');
  console.log('  - 确认Node.js版本 >= 18.19.0');
  console.log('  - 使用管理员权限运行命令');
  console.log('  - 清理npm缓存: npm cache clean --force\n');
  
  console.log('• 如果路径不正确:');
  console.log('  - 运行: npm list -g mcp-chrome-bridge');
  console.log('  - 手动查找安装目录');
  console.log('  - 确认mcp-server-stdio.js文件存在\n');
  
  console.log('• 如果连接失败:');
  console.log('  - 确认Chrome扩展已安装并连接');
  console.log('  - 检查防火墙设置');
  console.log('  - 重启Chrome浏览器\n');
}

function main() {
  console.log('🎯 新电脑Chrome MCP Server设置向导\n');
  console.log('=' .repeat(60));
  
  // 检查安装
  checkInstallation();
  
  // 查找路径
  const validPaths = findGlobalInstallPath();
  
  // 生成配置
  generateConfig(validPaths);
  
  // 显示设置说明
  showSetupInstructions();
  
  // 故障排除
  showTroubleshooting();
  
  console.log('=' .repeat(60));
  console.log('🎉 设置向导完成！');
}

main();
