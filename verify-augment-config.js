#!/usr/bin/env node

/**
 * 验证Augment MCP配置的脚本
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';

function checkGlobalInstallation() {
  console.log('🔍 检查全局安装状态...\n');
  
  // 检查Windows npm全局路径
  const npmGlobalPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge';
  const stdioServerPath = path.join(npmGlobalPath, 'dist', 'mcp', 'mcp-server-stdio.js');
  
  console.log('📍 检查路径:');
  console.log(`   ${npmGlobalPath}`);
  
  if (fs.existsSync(npmGlobalPath)) {
    console.log('✅ 全局安装路径存在');
    
    if (fs.existsSync(stdioServerPath)) {
      console.log('✅ STDIO服务器文件存在');
      console.log(`   ${stdioServerPath}`);
    } else {
      console.log('❌ STDIO服务器文件不存在');
    }
  } else {
    console.log('❌ 全局安装路径不存在');
  }
  
  return { npmGlobalPath, stdioServerPath, exists: fs.existsSync(stdioServerPath) };
}

function generateAugmentConfigs(paths) {
  console.log('\n📋 生成Augment配置...\n');
  
  // HTTP配置
  const httpConfig = {
    mcpServers: {
      "chrome-mcp-server": {
        type: "streamableHttp",
        url: "http://127.0.0.1:12306/mcp",
        name: "Chrome浏览器控制",
        description: "通过Chrome扩展控制浏览器的MCP服务器"
      }
    }
  };
  
  console.log('🌐 方法1: Streamable HTTP配置 (推荐)');
  console.log('```json');
  console.log(JSON.stringify(httpConfig, null, 2));
  console.log('```\n');
  
  // STDIO配置
  if (paths.exists) {
    const stdioConfig = {
      mcpServers: {
        "chrome-mcp-stdio": {
          command: "node",
          args: [paths.stdioServerPath.replace(/\\/g, '\\\\')],
          name: "Chrome浏览器控制(STDIO)",
          description: "通过STDIO连接的Chrome MCP服务器"
        }
      }
    };
    
    console.log('💻 方法2: STDIO配置');
    console.log('```json');
    console.log(JSON.stringify(stdioConfig, null, 2));
    console.log('```\n');
  } else {
    console.log('❌ STDIO配置不可用（文件不存在）\n');
  }
}

function checkServerStatus() {
  console.log('🔍 检查服务器状态...\n');
  
  return new Promise((resolve) => {
    const child = spawn('netstat', ['-an'], { shell: true });
    let output = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.on('close', (code) => {
      const isRunning = output.includes('127.0.0.1:12306');
      
      if (isRunning) {
        console.log('✅ MCP服务器正在运行 (端口12306)');
      } else {
        console.log('❌ MCP服务器未运行');
        console.log('💡 启动命令: mcp-chrome-bridge');
      }
      
      resolve(isRunning);
    });
    
    child.on('error', () => {
      console.log('⚠️  无法检查服务器状态');
      resolve(false);
    });
  });
}

function showSetupInstructions() {
  console.log('\n📝 完整设置步骤:\n');
  
  console.log('1️⃣ 启动MCP服务器:');
  console.log('   mcp-chrome-bridge\n');
  
  console.log('2️⃣ 安装Chrome扩展:');
  console.log('   • 打开 chrome://extensions/');
  console.log('   • 启用"开发者模式"');
  console.log('   • 加载文件夹: D:\\Test\\mcp-chrome\\app\\chrome-extension\\.output\\chrome-mv3\n');
  
  console.log('3️⃣ 连接扩展:');
  console.log('   • 点击扩展图标');
  console.log('   • 点击"Connect"按钮\n');
  
  console.log('4️⃣ 在Augment中添加上述配置\n');
  
  console.log('5️⃣ 测试命令:');
  console.log('   "请帮我截取当前网页的截图"');
  console.log('   "请列出我当前打开的所有浏览器标签页"\n');
}

function showTroubleshooting() {
  console.log('🚨 故障排除:\n');
  
  console.log('• 如果连接失败:');
  console.log('  - 确认服务器运行: netstat -an | findstr 12306');
  console.log('  - 检查Chrome扩展连接状态\n');
  
  console.log('• 如果工具调用失败:');
  console.log('  - 确认Chrome扩展已连接');
  console.log('  - 检查目标网页已加载完成\n');
  
  console.log('• 查看详细日志:');
  console.log('  - mcp-chrome-bridge --verbose\n');
}

async function main() {
  console.log('🎯 Augment MCP配置验证工具\n');
  console.log('=' .repeat(50));
  
  // 检查安装
  const paths = checkGlobalInstallation();
  
  // 生成配置
  generateAugmentConfigs(paths);
  
  // 检查服务器状态
  await checkServerStatus();
  
  // 显示设置说明
  showSetupInstructions();
  
  // 故障排除
  showTroubleshooting();
  
  console.log('=' .repeat(50));
  console.log('🎉 配置验证完成！');
}

main().catch(console.error);
